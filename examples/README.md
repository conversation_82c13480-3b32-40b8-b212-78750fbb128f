# Dataset Examples

This directory contains practical examples demonstrating how to use the dataset package for creating datasets and views.

## Examples

### 1. Simple Dataset Example (`simple_dataset_example.go`)

A basic example showing:
- Creating a dataset with student data
- Adding different column types (int, float, string)
- Creating views for subsets of data
- Safe pointer dereferencing
- Target distribution analysis

**Run with:**
```bash
cd examples
go run simple_dataset_example.go
```

### 2. Comprehensive Dataset Example (`dataset_view_example.go`)

A complete example demonstrating:
- Full dataset creation workflow
- Multiple view operations
- Child view creation
- Feature distribution analysis
- Error handling patterns
- Numerical value access
- Helper functions for safe value extraction

**Run with:**
```bash
cd examples
go run dataset_view_example.go
```

## Key Concepts Demonstrated

### Dataset Creation
- Initialize with capacity hint
- Add typed columns (int64, float64, string)
- Handle null masks for missing values
- Add target values

### View Operations
- Create views with specific row indices
- Use logical indexing within views
- Create child views from parent views
- Access data safely with pointer dereferencing

### Error Handling
- Pointer-based error handling
- Safe type assertions
- Bounds checking
- Missing value handling

### Performance Features
- Cached target distributions
- Cached feature distributions
- Memory-efficient view operations
- O(1) feature access

## Expected Output

Both examples will show:
1. Dataset creation confirmation
2. View creation with size information
3. Data access through logical indices
4. Target and feature distributions
5. Error handling demonstrations
6. Successful completion message

## Integration with ML Algorithms

These examples show patterns useful for:
- Decision tree algorithms
- Feature analysis
- Data splitting and subset operations
- Performance distribution analysis
- Safe numerical value access

## Dependencies

Examples require:
- `github.com/berrijam/mulberri/internal/data/dataset`
- `github.com/berrijam/mulberri/internal/data/features`
- Go standard library (`fmt`)

Make sure to run from the project root or adjust import paths accordingly.
