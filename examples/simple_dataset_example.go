package main

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/dataset"
)

// Simple example demonstrating basic dataset and view creation
func main() {
	fmt.Println("=== Simple Dataset and View Example ===")

	// Create a dataset for student grades
	ds := dataset.NewDataset[string](10)

	// Sample student data
	ages := []int64{20, 21, 19, 22, 20}
	grades := []float64{85.5, 92.0, 78.5, 88.0, 95.5}
	majors := []string{"CS", "Math", "CS", "Physics", "Math"}
	
	// No missing values in this simple example
	noNulls := []bool{false, false, false, false, false}
	
	// Performance categories
	performance := []string{"Good", "Excellent", "Average", "Good", "Excellent"}

	// Add data to dataset
	ds.AddIntColumn("age", ages, noNulls)
	ds.AddFloatColumn("grade", grades, noNulls)
	ds.AddStringColumn("major", majors, noNulls)
	
	for _, perf := range performance {
		ds.AddTarget(perf)
	}

	fmt.Printf("Created dataset with %d students\n", ds.GetRowCount())

	// Create a view for CS students only
	csIndices := []int{0, 2} // Students at indices 0 and 2 are CS majors
	csView := ds.CreateView(csIndices)

	fmt.Printf("\nCS Students View (%d students):\n", csView.GetSize())
	
	// Display CS students data
	for i := 0; i < csView.GetSize(); i++ {
		age := csView.GetFeatureValue(i, "age")
		grade := csView.GetFeatureValue(i, "grade")
		major := csView.GetFeatureValue(i, "major")
		perf := csView.GetTarget(i)

		// Safe dereferencing
		if agePtr, ok := age.(*int64); ok && agePtr != nil {
			if gradePtr, ok := grade.(*float64); ok && gradePtr != nil {
				if majorPtr, ok := major.(*string); ok && majorPtr != nil {
					fmt.Printf("  Student %d: Age=%d, Grade=%.1f, Major=%s, Performance=%s\n",
						i, *agePtr, *gradePtr, *majorPtr, perf)
				}
			}
		}
	}

	// Show target distribution
	dist := csView.GetTargetDistribution()
	fmt.Printf("\nCS Students Performance Distribution: %v\n", dist)

	// Create a child view for high-performing CS students (grade >= 85)
	highPerformers := []int{0} // Only logical index 0 in CS view has grade >= 85
	highPerfView := csView.CreateChildView(highPerformers)

	fmt.Printf("\nHigh-Performing CS Students (%d students):\n", highPerfView.GetSize())
	for i := 0; i < highPerfView.GetSize(); i++ {
		grade := highPerfView.GetFeatureValue(i, "grade")
		perf := highPerfView.GetTarget(i)
		
		if gradePtr, ok := grade.(*float64); ok && gradePtr != nil {
			fmt.Printf("  Student %d: Grade=%.1f, Performance=%s\n", i, *gradePtr, perf)
		}
	}

	fmt.Println("\n=== Example Complete ===")
}
