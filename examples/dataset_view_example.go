package main

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/dataset"
)

func mainn() {
	// Step 1: Create a new dataset with capacity hint
	ds := dataset.NewDataset[string](100)
	fmt.Println("Created empty dataset")

	// Step 2: Prepare sample data
	// Employee data with age, salary, and department
	ages := []int64{25, 30, 35, 40, 45, 28, 33, 38}
	salaries := []float64{50000.0, 60000.0, 70000.0, 80000.0, 90000.0, 55000.0, 65000.0, 75000.0}
	departments := []string{"Engineering", "Marketing", "Engineering", "Sales", "Engineering", "Marketing", "Sales", "Engineering"}

	// Null masks (false = valid data, true = missing data)
	ageNulls := []bool{false, false, false, false, false, false, false, false}
	salaryNulls := []bool{false, false, false, false, false, false, false, false}
	deptNulls := []bool{false, false, false, false, false, false, false, false}

	// Target values (performance ratings)
	targets := []string{"Good", "Excellent", "Good", "Average", "Excellent", "Good", "Average", "Excellent"}

	// Step 3: Add columns to dataset
	ds.AddIntColumn("age", ages, ageNulls)
	ds.AddFloatColumn("salary", salaries, salaryNulls)
	ds.AddStringColumn("department", departments, deptNulls)

	// Add targets
	for _, target := range targets {
		ds.AddTarget(target)
	}

	fmt.Printf("Dataset created with %d rows and %d features\n", ds.GetRowCount(), len(ds.GetFeatureOrder()))
	fmt.Printf("Features: %v\n", ds.GetFeatureOrder())

	// Step 4: Create a view with specific rows (Engineering employees)
	engineeringIndices := []int{0, 2, 4, 7} // Physical indices of Engineering employees
	engineeringView := ds.CreateView(engineeringIndices)

	fmt.Printf("\nCreated Engineering view with %d employees\n", engineeringView.GetSize())
	fmt.Printf("View includes physical indices: %v\n", engineeringView.GetActiveIndices())

	// Step 5: Access data through the view using logical indices
	fmt.Println("\nEngineering employees data:")
	for i := 0; i < engineeringView.GetSize(); i++ {
		// Get feature values using logical index i
		ageValue := engineeringView.GetFeatureValue(i, "age")
		salaryValue := engineeringView.GetFeatureValue(i, "salary")
		deptValue := engineeringView.GetFeatureValue(i, "department")
		target := engineeringView.GetTarget(i)

		// Safe pointer dereferencing
		var age int64
		var salary float64
		var dept string

		if agePtr, ok := ageValue.(*int64); ok && agePtr != nil {
			age = *agePtr
		}
		if salaryPtr, ok := salaryValue.(*float64); ok && salaryPtr != nil {
			salary = *salaryPtr
		}
		if deptPtr, ok := deptValue.(*string); ok && deptPtr != nil {
			dept = *deptPtr
		}

		fmt.Printf("  Employee %d: Age=%d, Salary=%.0f, Dept=%s, Rating=%s\n",
			i, age, salary, dept, target)
	}

	// Step 6: Get target distribution for the view
	targetDist := engineeringView.GetTargetDistribution()
	fmt.Printf("\nEngineering team performance distribution: %v\n", targetDist)

	// Step 7: Create a child view (senior engineers: age >= 35)
	seniorIndices := []int{1, 2, 3} // Logical indices in engineering view (ages 35, 45, 38)
	seniorView := engineeringView.CreateChildView(seniorIndices)

	fmt.Printf("\nCreated senior engineers view with %d employees\n", seniorView.GetSize())
	fmt.Printf("Senior view physical indices: %v\n", seniorView.GetActiveIndices())

	// Step 8: Analyze senior engineers
	fmt.Println("\nSenior Engineering employees:")
	for i := 0; i < seniorView.GetSize(); i++ {
		ageValue := seniorView.GetFeatureValue(i, "age")
		target := seniorView.GetTarget(i)

		if agePtr, ok := ageValue.(*int64); ok && agePtr != nil {
			fmt.Printf("  Senior Employee %d: Age=%d, Rating=%s\n", i, *agePtr, target)
		}
	}

	seniorTargetDist := seniorView.GetTargetDistribution()
	fmt.Printf("Senior engineers performance distribution: %v\n", seniorTargetDist)

	// Step 9: Feature distribution analysis
	fmt.Println("\nFeature distribution analysis:")

	// Age distribution in engineering view
	ageDist := engineeringView.GetFeatureDistribution("age")
	if ageDist != nil {
		entries := ageDist.GetEntries()
		for _, entry := range entries {
			fmt.Printf("  Age %v: %d employees\n", entry.Value, entry.Count)
		}
	}

	// Step 10: Error handling demonstration
	fmt.Println("\nError handling examples:")

	// Invalid logical index
	invalidValue := engineeringView.GetFeatureValue(999, "age")
	if invalidValue == nil {
		fmt.Println("✓ Invalid index correctly returned nil")
	}

	// Non-existent feature
	missingFeature := engineeringView.GetFeatureValue(0, "nonexistent")
	if missingFeature == nil {
		fmt.Println("✓ Missing feature correctly returned nil")
	}

	// Step 11: Numerical value access for ML algorithms
	fmt.Println("\nNumerical value access:")
	col := ds.GetColumn("age")
	if col != nil && col.IsNumerical() {
		numValue := col.GetNumericalValue(0)
		if numValue != nil {
			fmt.Printf("Age as numerical value: %.1f\n", *numValue)
		}
	}

	fmt.Println("\nExample completed successfully!")
}

// Helper function for safe feature value extraction
func safeGetIntValue(view *dataset.DatasetView[string], index int, feature string) (int64, bool) {
	value := view.GetFeatureValue(index, feature)
	if value == nil {
		return 0, false
	}

	if intPtr, ok := value.(*int64); ok && intPtr != nil {
		return *intPtr, true
	}

	return 0, false
}

func safeGetFloatValue(view *dataset.DatasetView[string], index int, feature string) (float64, bool) {
	value := view.GetFeatureValue(index, feature)
	if value == nil {
		return 0.0, false
	}

	if floatPtr, ok := value.(*float64); ok && floatPtr != nil {
		return *floatPtr, true
	}

	return 0.0, false
}

func safeGetStringValue(view *dataset.DatasetView[string], index int, feature string) (string, bool) {
	value := view.GetFeatureValue(index, feature)
	if value == nil {
		return "", false
	}

	if stringPtr, ok := value.(*string); ok && stringPtr != nil {
		return *stringPtr, true
	}

	return "", false
}
